/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
import model.KhachHang;
import model.KhachHangDAO;

import java.util.List;

public class TestConnection {
    public static void main(String[] args) {
        List<KhachHang> ds = KhachHangDAO.getAllKhachHang();

        if (ds.isEmpty()) {
            System.out.println("❌ Không có dữ liệu khách hàng.");
        } else {
            System.out.println("✅ Danh sách khách hàng:");
            for (KhachHang kh : ds) {
                System.out.println(kh);
            }
        }
    }
}

