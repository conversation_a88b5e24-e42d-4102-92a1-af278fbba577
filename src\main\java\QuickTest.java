import util.DBConnection;
import model.KhachHangDAO;
import model.KhachHang;

import java.sql.Connection;
import java.util.List;

public class QuickTest {
    public static void main(String[] args) {
        System.out.println("=== KIỂM TRA KẾT NỐI DATABASE ===");
        
        // 1. Test kết nối
        System.out.println("1. Kiểm tra kết nối...");
        Connection conn = DBConnection.getConnection();
        if (conn != null) {
            System.out.println("   ✅ Kết nối thành công!");
            try {
                System.out.println("   📍 Database: " + conn.getCatalog());
                conn.close();
            } catch (Exception e) {
                System.out.println("   ⚠️ Lỗi khi đóng kết nối: " + e.getMessage());
            }
        } else {
            System.out.println("   ❌ Kết nối thất bại!");
            System.out.println("   💡 Kiểm tra:");
            System.out.println("      - SQL Server có chạy không?");
            System.out.println("      - Database QLGU có tồn tại không?");
            System.out.println("      - Username/password có đúng không?");
            return;
        }
        
        // 2. Test lấy dữ liệu
        System.out.println("\n2. Kiểm tra dữ liệu khách hàng...");
        try {
            List<KhachHang> danhSach = KhachHangDAO.getAllKhachHang();
            System.out.println("   📊 Tìm thấy: " + danhSach.size() + " khách hàng");
            
            if (danhSach.isEmpty()) {
                System.out.println("   ❌ Không có dữ liệu!");
                System.out.println("   💡 Hãy chạy script SQL sau trong SQL Server:");
                System.out.println("      USE QLGU;");
                System.out.println("      INSERT INTO KhachHang (ten, sdt, diachi) VALUES");
                System.out.println("      (N'Nguyễn Văn A', '**********', N'123 ABC, Q1, HCM'),");
                System.out.println("      (N'Trần Thị B', '**********', N'456 XYZ, Q2, HCM');");
            } else {
                System.out.println("   ✅ Danh sách khách hàng:");
                for (int i = 0; i < Math.min(danhSach.size(), 5); i++) {
                    KhachHang kh = danhSach.get(i);
                    System.out.println("      " + (i+1) + ". " + kh.getTen() + " - " + kh.getSdt());
                }
                if (danhSach.size() > 5) {
                    System.out.println("      ... và " + (danhSach.size() - 5) + " khách hàng khác");
                }
            }
        } catch (Exception e) {
            System.out.println("   ❌ Lỗi khi lấy dữ liệu: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println("\n=== KẾT THÚC KIỂM TRA ===");
    }
}
