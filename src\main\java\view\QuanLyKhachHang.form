<?xml version="1.0" encoding="UTF-8" ?>

<Form version="1.3" maxVersion="1.9" type="org.netbeans.modules.form.forminfo.JPanelFormInfo">
  <NonVisualComponents>
    <Container class="javax.swing.JPanel" name="jPanel2">

      <Layout>
        <DimensionLayout dim="0">
          <Group type="103" groupAlignment="0" attributes="0">
              <EmptySpace min="0" pref="100" max="32767" attributes="0"/>
          </Group>
        </DimensionLayout>
        <DimensionLayout dim="1">
          <Group type="103" groupAlignment="0" attributes="0">
              <EmptySpace min="0" pref="100" max="32767" attributes="0"/>
          </Group>
        </DimensionLayout>
      </Layout>
    </Container>
  </NonVisualComponents>
  <AuxValues>
    <AuxValue name="FormSettings_autoResourcing" type="java.lang.Integer" value="0"/>
    <AuxValue name="FormSettings_autoSetComponentName" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_generateFQN" type="java.lang.Boolean" value="true"/>
    <AuxValue name="FormSettings_generateMnemonicsCode" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_i18nAutoMode" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_layoutCodeTarget" type="java.lang.Integer" value="1"/>
    <AuxValue name="FormSettings_listenerGenerationStyle" type="java.lang.Integer" value="0"/>
    <AuxValue name="FormSettings_variablesLocal" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_variablesModifier" type="java.lang.Integer" value="2"/>
  </AuxValues>

  <Layout>
    <DimensionLayout dim="0">
      <Group type="103" groupAlignment="0" attributes="0">
          <Group type="102" attributes="0">
              <EmptySpace min="-2" pref="326" max="-2" attributes="0"/>
              <Component id="jLabel1" min="-2" pref="275" max="-2" attributes="0"/>
              <EmptySpace max="-2" attributes="0"/>
              <Component id="jPanel1" max="32767" attributes="0"/>
              <EmptySpace max="-2" attributes="0"/>
          </Group>
          <Group type="102" attributes="0">
              <EmptySpace max="-2" attributes="0"/>
              <Group type="103" groupAlignment="0" attributes="0">
                  <Component id="jPanel3" max="32767" attributes="0"/>
                  <Component id="jPanel4" max="32767" attributes="0"/>
              </Group>
          </Group>
      </Group>
    </DimensionLayout>
    <DimensionLayout dim="1">
      <Group type="103" groupAlignment="0" attributes="0">
          <Group type="102" alignment="0" attributes="0">
              <Group type="103" groupAlignment="0" attributes="0">
                  <Component id="jPanel1" min="-2" max="-2" attributes="0"/>
                  <Group type="102" alignment="0" attributes="0">
                      <EmptySpace min="-2" pref="23" max="-2" attributes="0"/>
                      <Component id="jLabel1" min="-2" max="-2" attributes="0"/>
                  </Group>
              </Group>
              <EmptySpace min="-2" pref="16" max="-2" attributes="0"/>
              <Component id="jPanel3" min="-2" max="-2" attributes="0"/>
              <EmptySpace type="separate" max="-2" attributes="0"/>
              <Component id="jPanel4" min="-2" max="-2" attributes="0"/>
              <EmptySpace max="32767" attributes="0"/>
          </Group>
      </Group>
    </DimensionLayout>
  </Layout>
  <SubComponents>
    <Container class="javax.swing.JPanel" name="jPanel1">

      <Layout>
        <DimensionLayout dim="0">
          <Group type="103" groupAlignment="0" attributes="0">
              <EmptySpace min="0" pref="126" max="32767" attributes="0"/>
          </Group>
        </DimensionLayout>
        <DimensionLayout dim="1">
          <Group type="103" groupAlignment="0" attributes="0">
              <EmptySpace min="0" pref="44" max="32767" attributes="0"/>
          </Group>
        </DimensionLayout>
      </Layout>
    </Container>
    <Component class="javax.swing.JLabel" name="jLabel1">
      <Properties>
        <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
          <Font name="Segoe UI" size="24" style="0"/>
        </Property>
        <Property name="foreground" type="java.awt.Color" editor="org.netbeans.beaninfo.editors.ColorEditor">
          <Color blue="ff" green="0" red="0" type="rgb"/>
        </Property>
        <Property name="text" type="java.lang.String" value="Qu&#x1ea3;n L&#xfd; Kh&#xe1;ch H&#xe0;ng "/>
      </Properties>
    </Component>
    <Container class="javax.swing.JPanel" name="jPanel3">

      <Layout>
        <DimensionLayout dim="0">
          <Group type="103" groupAlignment="0" attributes="0">
              <Group type="102" alignment="0" attributes="0">
                  <EmptySpace min="-2" pref="92" max="-2" attributes="0"/>
                  <Group type="103" groupAlignment="0" attributes="0">
                      <Group type="102" alignment="0" attributes="0">
                          <Group type="103" groupAlignment="1" attributes="0">
                              <Group type="102" attributes="0">
                                  <Component id="jLabel3" min="-2" max="-2" attributes="0"/>
                                  <EmptySpace min="-2" pref="53" max="-2" attributes="0"/>
                              </Group>
                              <Group type="103" alignment="1" groupAlignment="0" attributes="0">
                                  <Component id="jLabel5" min="-2" max="-2" attributes="0"/>
                                  <Component id="jLabel4" min="-2" max="-2" attributes="0"/>
                              </Group>
                          </Group>
                          <Group type="103" groupAlignment="0" attributes="0">
                              <Group type="102" attributes="0">
                                  <Component id="btnxoa" min="-2" max="-2" attributes="0"/>
                                  <EmptySpace min="-2" pref="107" max="-2" attributes="0"/>
                                  <Component id="btnsua" min="-2" max="-2" attributes="0"/>
                                  <EmptySpace max="32767" attributes="0"/>
                                  <Component id="btnthem" min="-2" max="-2" attributes="0"/>
                              </Group>
                              <Group type="103" alignment="1" groupAlignment="0" max="-2" attributes="0">
                                  <Component id="txtsdt" alignment="1" pref="411" max="32767" attributes="0"/>
                                  <Component id="txthoten" alignment="1" max="32767" attributes="0"/>
                                  <Component id="txtdiachi" alignment="1" max="32767" attributes="0"/>
                              </Group>
                          </Group>
                          <EmptySpace max="32767" attributes="0"/>
                      </Group>
                      <Group type="102" alignment="0" attributes="0">
                          <EmptySpace min="-2" pref="2" max="-2" attributes="0"/>
                          <Component id="jLabel2" min="-2" pref="174" max="-2" attributes="0"/>
                          <EmptySpace max="32767" attributes="0"/>
                      </Group>
                  </Group>
              </Group>
          </Group>
        </DimensionLayout>
        <DimensionLayout dim="1">
          <Group type="103" groupAlignment="0" attributes="0">
              <Group type="102" attributes="0">
                  <EmptySpace min="-2" pref="44" max="-2" attributes="0"/>
                  <Group type="103" groupAlignment="3" attributes="0">
                      <Component id="jLabel3" alignment="3" min="-2" max="-2" attributes="0"/>
                      <Component id="txtdiachi" alignment="3" min="-2" max="-2" attributes="0"/>
                  </Group>
                  <EmptySpace min="-2" pref="27" max="-2" attributes="0"/>
                  <Group type="103" groupAlignment="3" attributes="0">
                      <Component id="jLabel4" alignment="3" min="-2" max="-2" attributes="0"/>
                      <Component id="txthoten" alignment="3" min="-2" max="-2" attributes="0"/>
                  </Group>
                  <EmptySpace pref="34" max="32767" attributes="0"/>
                  <Group type="103" groupAlignment="3" attributes="0">
                      <Component id="jLabel5" alignment="3" min="-2" max="-2" attributes="0"/>
                      <Component id="txtsdt" alignment="3" min="-2" max="-2" attributes="0"/>
                  </Group>
                  <EmptySpace min="-2" pref="35" max="-2" attributes="0"/>
                  <Group type="103" groupAlignment="3" attributes="0">
                      <Component id="btnxoa" alignment="3" min="-2" max="-2" attributes="0"/>
                      <Component id="btnthem" alignment="3" min="-2" max="-2" attributes="0"/>
                      <Component id="btnsua" alignment="3" min="-2" max="-2" attributes="0"/>
                  </Group>
                  <EmptySpace min="-2" pref="34" max="-2" attributes="0"/>
                  <Component id="jLabel2" min="-2" max="-2" attributes="0"/>
              </Group>
          </Group>
        </DimensionLayout>
      </Layout>
      <SubComponents>
        <Component class="javax.swing.JButton" name="btnthem">
          <Properties>
            <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
              <Font name="Segoe UI" size="16" style="0"/>
            </Property>
            <Property name="text" type="java.lang.String" value="Th&#xea;m "/>
          </Properties>
        </Component>
        <Component class="javax.swing.JButton" name="btnxoa">
          <Properties>
            <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
              <Font name="Segoe UI" size="16" style="0"/>
            </Property>
            <Property name="text" type="java.lang.String" value="Xo&#xe1;"/>
          </Properties>
        </Component>
        <Component class="javax.swing.JButton" name="btnsua">
          <Properties>
            <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
              <Font name="Segoe UI" size="16" style="0"/>
            </Property>
            <Property name="text" type="java.lang.String" value="S&#x1eed;a"/>
          </Properties>
        </Component>
        <Component class="javax.swing.JTextField" name="txthoten">
        </Component>
        <Component class="javax.swing.JTextField" name="txtsdt">
        </Component>
        <Component class="javax.swing.JTextField" name="txtdiachi">
        </Component>
        <Component class="javax.swing.JLabel" name="jLabel3">
          <Properties>
            <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
              <Font name="Segoe UI" size="16" style="0"/>
            </Property>
            <Property name="text" type="java.lang.String" value="H&#x1ecd; T&#xea;n"/>
          </Properties>
        </Component>
        <Component class="javax.swing.JLabel" name="jLabel4">
          <Properties>
            <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
              <Font name="Segoe UI" size="16" style="0"/>
            </Property>
            <Property name="text" type="java.lang.String" value="S&#x1ed1; &#x110;i&#x1ec7;n Tho&#x1ea1;i "/>
          </Properties>
        </Component>
        <Component class="javax.swing.JLabel" name="jLabel5">
          <Properties>
            <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
              <Font name="Segoe UI" size="16" style="0"/>
            </Property>
            <Property name="text" type="java.lang.String" value="&#x110;&#x1ecb;a Ch&#x1ec9;"/>
          </Properties>
        </Component>
        <Component class="javax.swing.JLabel" name="jLabel2">
          <Properties>
            <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
              <Font name="Segoe UI" size="16" style="0"/>
            </Property>
            <Property name="text" type="java.lang.String" value="Chi Ti&#x1ebf;t Kh&#xe1;ch H&#xe0;ng"/>
          </Properties>
        </Component>
      </SubComponents>
    </Container>
    <Container class="javax.swing.JPanel" name="jPanel4">

      <Layout>
        <DimensionLayout dim="0">
          <Group type="103" groupAlignment="0" attributes="0">
              <Group type="102" alignment="1" attributes="0">
                  <EmptySpace max="32767" attributes="0"/>
                  <Component id="jScrollPane1" min="-2" pref="588" max="-2" attributes="0"/>
                  <EmptySpace min="-2" pref="68" max="-2" attributes="0"/>
              </Group>
          </Group>
        </DimensionLayout>
        <DimensionLayout dim="1">
          <Group type="103" groupAlignment="0" attributes="0">
              <Group type="102" alignment="0" attributes="0">
                  <EmptySpace max="-2" attributes="0"/>
                  <Component id="jScrollPane1" min="-2" pref="255" max="-2" attributes="0"/>
                  <EmptySpace pref="178" max="32767" attributes="0"/>
              </Group>
          </Group>
        </DimensionLayout>
      </Layout>
      <SubComponents>
        <Container class="javax.swing.JScrollPane" name="jScrollPane1">
          <AuxValues>
            <AuxValue name="autoScrollPane" type="java.lang.Boolean" value="true"/>
          </AuxValues>

          <Layout class="org.netbeans.modules.form.compat2.layouts.support.JScrollPaneSupportLayout"/>
          <SubComponents>
            <Component class="javax.swing.JTable" name="tblchitietkhachhang">
              <Properties>
                <Property name="model" type="javax.swing.table.TableModel" editor="org.netbeans.modules.form.editors2.TableModelEditor">
                  <Table columnCount="3" rowCount="3">
                    <Column editable="true" title="H&#x1ecd; T&#xea;n" type="java.lang.Object"/>
                    <Column editable="true" title="S&#x1ed1; &#x110;i&#x1ec7;n Tho&#x1ea1;i" type="java.lang.Object"/>
                    <Column editable="true" title="&#x110;&#x1ecb;a Ch&#x1ec9;" type="java.lang.Object"/>
                  </Table>
                </Property>
              </Properties>
            </Component>
          </SubComponents>
        </Container>
      </SubComponents>
    </Container>
  </SubComponents>
</Form>
