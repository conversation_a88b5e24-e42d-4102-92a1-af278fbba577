import view.QuanLyKhachHang;
import javax.swing.*;

public class TestKhachHangForm {
    public static void main(String[] args) {
        System.out.println("🚀 Test form Quản Lý Khách Hàng...");
        
        SwingUtilities.invokeLater(() -> {
            try {
                // Tạo JFrame để chứa form
                JFrame frame = new JFrame("Test - Quản Lý Khách Hàng");
                frame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
                frame.setSize(900, 700);
                frame.setLocationRelativeTo(null);
                
                // Thêm form QuanLyKhachHang
                System.out.println("📋 Tạo form QuanLyKhachHang...");
                QuanLyKhachHang form = new QuanLyKhachHang();
                frame.add(form);
                
                // Hiển thị
                frame.setVisible(true);
                System.out.println("✅ Form đã hiển thị!");
                
            } catch (Exception e) {
                System.err.println("❌ Lỗi: " + e.getMessage());
                e.printStackTrace();
            }
        });
    }
}
