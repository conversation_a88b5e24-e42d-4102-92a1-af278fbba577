/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package model;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;
import util.DBConnection;


public class KhachHangDAO {
    
       public static List<KhachHang> getAllKhachHang() {
    List<KhachHang> list = new ArrayList<>();
    String sql = "SELECT * FROM KhachHang";

    try (Connection conn = DBConnection.getConnection();
         Statement stmt = conn.createStatement();
         ResultSet rs = stmt.executeQuery(sql)) {

        while (rs.next()) {
            KhachHang kh = new KhachHang(
                rs.getInt("id"),
                rs.getNString("ten"),
                rs.getString("sdt"),
                rs.getNString("diachi")
            );
            list.add(kh);
        }

    } catch (SQLException e) {
        e.printStackTrace();
    }

    return list;
}

}
 