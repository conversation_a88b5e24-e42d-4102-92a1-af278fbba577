import view.MainFrame;
import view.QuanLyKhachHang;
import model.KhachHangDAO;
import util.DBConnection;

import javax.swing.*;
import java.sql.Connection;

public class TestApp {
    public static void main(String[] args) {
        System.out.println("🚀 Khởi động ứng dụng Quản Lý Giặt Ủi...");
        
        // Test kết nối database trước
        System.out.println("🔄 Kiểm tra kết nối database...");
        Connection conn = DBConnection.getConnection();
        if (conn == null) {
            System.err.println("❌ Không thể kết nối database!");
            JOptionPane.showMessageDialog(null, 
                "Không thể kết nối database!\n" +
                "Vui lòng kiểm tra:\n" +
                "1. SQL Server đã chạy chưa?\n" +
                "2. Database QLGU có tồn tại không?\n" +
                "3. Thông tin kết nối trong DBConnection.java có đúng không?", 
                "Lỗi Database", 
                JOptionPane.ERROR_MESSAGE);
            return;
        } else {
            System.out.println("✅ Kết nối database thành công!");
            try {
                conn.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        
        // Test lấy dữ liệu
        System.out.println("🔄 Test lấy dữ liệu khách hàng...");
        try {
            var khachHangs = KhachHangDAO.getAllKhachHang();
            System.out.println("📊 Tìm thấy " + khachHangs.size() + " khách hàng");
            
            if (khachHangs.isEmpty()) {
                System.out.println("⚠️ Chưa có dữ liệu khách hàng!");
                int choice = JOptionPane.showConfirmDialog(null,
                    "Chưa có dữ liệu khách hàng trong database.\n" +
                    "Bạn có muốn chạy script tạo dữ liệu mẫu không?\n\n" +
                    "Nếu chọn 'Yes', vui lòng chạy file database_setup.sql trong SQL Server Management Studio",
                    "Không có dữ liệu",
                    JOptionPane.YES_NO_OPTION,
                    JOptionPane.QUESTION_MESSAGE);
                    
                if (choice == JOptionPane.YES_OPTION) {
                    System.out.println("💡 Vui lòng chạy file database_setup.sql để tạo dữ liệu mẫu");
                }
            } else {
                for (var kh : khachHangs) {
                    System.out.println("  - " + kh.getTen());
                }
            }
        } catch (Exception e) {
            System.err.println("❌ Lỗi khi test dữ liệu: " + e.getMessage());
            e.printStackTrace();
        }
        
        // Khởi động giao diện
        SwingUtilities.invokeLater(() -> {
            try {
                // Set Look and Feel
                UIManager.setLookAndFeel(UIManager.getSystemLookAndFeel());
                
                System.out.println("🖥️ Khởi động giao diện...");
                MainFrame frame = new MainFrame();
                frame.setVisible(true);
                
                System.out.println("✅ Ứng dụng đã sẵn sàng!");
                
            } catch (Exception e) {
                System.err.println("❌ Lỗi khi khởi động giao diện: " + e.getMessage());
                e.printStackTrace();
                JOptionPane.showMessageDialog(null, 
                    "Lỗi khi khởi động ứng dụng:\n" + e.getMessage(), 
                    "Lỗi", 
                    JOptionPane.ERROR_MESSAGE);
            }
        });
    }
}
